package main

import (
	"fmt"
	"os/exec"
	"regexp"
	"strconv"
	"time"

	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func getHardDiskTemperature(device string) (string, error) {
	var cmd = exec.Command("smartctl", "-A", device)

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("警告: smartctl 返回错误: %v\n", err)
	}

	// 尝试多种温度匹配模式
	patterns := []string{
		`Temperature.*?(\d+)`,
		`Temp.*?(\d+)`,
		`温度.*?(\d+)`,
		`Airflow_Temperature.*?(\d+)`,
		`Temperature_Celsius.*?(\d+)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(string(output))
		if len(matches) > 1 {
			return matches[1], nil
		}
	}

	return "", fmt.Errorf("在输出中未找到温度信息")
}

// 添加一个发出声音警报的函数
func playAlarm() error {
	// Windows系统使用PowerShell的Beep命令发出声音
	cmd := exec.Command("PowerShell", "-Command", "[console]::beep(1000,500)")
	err := cmd.Run()

	// 如果PowerShell命令失败，尝试使用ASCII警报字符
	if err != nil {
		fmt.Print("\a") // ASCII 警报字符
	}

	return err
}

func main() {
	device := "/dev/sdc"

	// 创建应用和窗口
	myApp := app.New()
	myWindow := myApp.NewWindow("硬盘温度监控")
	myWindow.Resize(fyne.NewSize(400, 200))

	// 创建UI元素
	deviceLabel := widget.NewLabel("监控设备: " + device)
	tempLabel := widget.NewLabel("正在获取温度...")
	statusLabel := widget.NewLabel("状态: 正常")
	timeLabel := widget.NewLabel("上次更新: -")

	// 创建一个温度显示矩形
	tempRect := canvas.NewRectangle(color.NRGBA{R: 0, G: 200, B: 0, A: 255})
	tempRect.Resize(fyne.NewSize(50, 30))

	// 布局
	content := container.NewVBox(
		deviceLabel,
		container.NewHBox(widget.NewLabel("温度:"), tempLabel, tempRect),
		statusLabel,
		timeLabel,
	)

	myWindow.SetContent(content)

	// 启动一个goroutine来更新温度
	go func() {
		var lastTemp string

		for {
			time.Sleep(2 * time.Second)

			currentTime := time.Now().Format("2006-01-02 15:04:05")
			temp, err := getHardDiskTemperature(device)

			// 使用goroutine安全的方式更新UI
			myApp.SendNotification(&fyne.Notification{
				Title:   "更新",
				Content: "正在更新温度数据",
			})

			// 更新UI元素（这些操作是线程安全的）
			timeLabel.SetText("上次更新: " + currentTime)

			if err != nil {
				tempLabel.SetText("错误")
				statusLabel.SetText("状态: " + err.Error())
			} else {
				tempLabel.SetText(temp + "°C")

				// 检查温度变化
				if temp != lastTemp {
					statusLabel.SetText("状态: 温度已变化")
					lastTemp = temp
				} else {
					statusLabel.SetText("状态: 正常")
				}

				// 根据温度改变颜色
				tempInt, _ := strconv.Atoi(temp)
				if tempInt > 60 {
					tempRect.FillColor = color.NRGBA{R: 255, G: 0, B: 0, A: 255}
					statusLabel.SetText("状态: 警告! 温度过高!")
					playAlarm()
				} else if tempInt > 50 {
					tempRect.FillColor = color.NRGBA{R: 255, G: 165, B: 0, A: 255}
				} else {
					tempRect.FillColor = color.NRGBA{R: 0, G: 200, B: 0, A: 255}
				}
				tempRect.Refresh()
			}
		}
	}()

	myWindow.ShowAndRun()
}
